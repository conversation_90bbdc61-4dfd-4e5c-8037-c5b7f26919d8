using System.Data;

namespace IPIS.Repositories.Interfaces
{
    public interface ITrainRepository
    {
        DataTable GetAllTrains();
        DataTable GetOnlineTrains();
        DataTable GetTrainsForCurrentDay();
        DataTable GetTrainsForCurrentTimeWindow(int timeWindowMinutes = 120);
        DataTable GetTrainsForCurrentDayAndTime(int timeWindowMinutes = 120);
        void AddTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays);
        void UpdateTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays);
        void DeleteTrain(string trainNo);
        void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement);
        void DeleteOnlineTrain(string trainNo);
        void AddOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string eat, string edt, string pf, string an, string changedPF = "", string divertedFrom = "");
        void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement, string changedPF = "", string divertedFrom = "");
        void UpdateOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string expAT, string expDT, string platform, string announcement, string changedPF = "", string divertedFrom = "");
        void ClearAllOnlineTrains();
        void ClearAllTrains();
    }
}